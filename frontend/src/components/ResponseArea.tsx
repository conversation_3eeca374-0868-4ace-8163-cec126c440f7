import { useState, useEffect, useRef, useCallback } from "react";
import { Loader2 } from "lucide-react";
import FeedbackForm from "./FeedbackForm";
import { useSelectionStore } from "../store/selectionStore";
import { formatSummary } from "./formatSummary";
import ResponseTable from "./ResponseTable"; 
import ResponseControls from "./ResponseControls";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

type WalmartRow = { id_str?: string; [key: string]: any };

interface ResponseAreaProps { 
  preloadedRow: WalmartRow 
}

interface EnhancedFinalValue { 
  Attribute?: string; 
  finalValue: string; 
  source: string; 
  walmartValidation?: string; 
  llmValidation?: string; 
  walmartComment?: string; 
  llmComment?: string; 
  finalValidation?: string; 
  finalComment?: string; 
  finalVerdict?: string; // ✅ NEW
  rawData?: any 
}

export default function ResponseArea({ preloadedRow }: ResponseAreaProps) {
  const { 
    selections, 
    editableValues, 
    validations, 
    comments,
    finalVerdicts, // ✅ NEW
    setSelections, 
    clearSelections, 
    setEditableValues,
    setValidations,
    setComments,
    setFinalVerdicts // ✅ NEW
  } = useSelectionStore();

  const [loading, setLoading] = useState(false);
  const [tableRows, setTableRows] = useState<Record<string, string>[]>([]);
  const [rawJson, setRawJson] = useState("");
  const [shortSummary, setShortSummary] = useState("");
  const [detailedSummary, setDetailedSummary] = useState("");
  const [insightsGenerated, setInsightsGenerated] = useState(false);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [attrMap, setAttrMap] = useState<Record<string, string>>({});
  const [competitorUrls, setCompetitorUrls] = useState<Record<string, string>>({});
  const [brandUrl, setBrandUrl] = useState("");
  const [llmSourceUrl, setLlmSourceUrl] = useState("");

  const [selectedCells, setSelectedCells] = useState<Set<string>>(new Set());
  const [editableWalmartValues, setEditableWalmartValues] = useState<Record<number, string>>({});
  const [originalWalmartValues, setOriginalWalmartValues] = useState<Record<number, string>>({});
  const [llmValues, setLlmValues] = useState<Record<number, string>>({});
  const [editingCell, setEditingCell] = useState<number | null>(null);

  const [walmartValidation, setWalmartValidation] = useState<Record<number, string>>({});
  const [llmValidation, setLlmValidation] = useState<Record<number, string>>({});
  const [walmartComments, setWalmartComments] = useState<Record<number, string>>({});
  const [llmComments, setLlmComments] = useState<Record<number, string>>({});
  const [finalVerdictsState, setFinalVerdictsState] = useState<Record<number, string>>({}); // ✅ NEW
  const [editingFinalVerdict, setEditingFinalVerdict] = useState<number | null>(null); // ✅ NEW

  const currentId = preloadedRow?.id_str || '';
  
  const loadedRef = useRef(false);
  const lastIdRef = useRef('');
  const initialLoadRef = useRef(false); // ✅ NEW: Track if initial load happened

  // ✅ Load saved state ONLY when ID changes
  useEffect(() => {
    if (currentId && currentId !== lastIdRef.current) {
      lastIdRef.current = currentId;
      loadedRef.current = false;
      
      const savedSelections = selections[currentId] || [];
      setSelectedCells(new Set(savedSelections));
      
      const savedEditableValues = editableValues[currentId] || {};
      setEditableWalmartValues(savedEditableValues);
      
      const savedValidations = validations[currentId] || { walmart: {}, llm: {} };
      setWalmartValidation(savedValidations.walmart || {});
      setLlmValidation(savedValidations.llm || {});
      
      const savedComments = comments[currentId] || { walmart: {}, llm: {} };
      setWalmartComments(savedComments.walmart || {});
      setLlmComments(savedComments.llm || {});

      // ✅ NEW: Load final verdicts
      const savedFinalVerdicts = finalVerdicts[currentId] || {};
      setFinalVerdictsState(savedFinalVerdicts);
      
      loadedRef.current = true;
    }
  }, [currentId, selections, editableValues, validations, comments, finalVerdicts]);

  useEffect(() => {
    
    if (preloadedRow?.id_str && !initialLoadRef.current) {
      initialLoadRef.current = true;
      fetchResponse();
    }
  }, [preloadedRow?.id_str]); 

  useEffect(() => {
    if (preloadedRow?.id_str && 
        preloadedRow.id_str !== lastIdRef.current && 
        initialLoadRef.current) { 
      fetchResponse();
    }
  }, [preloadedRow?.id_str]);

  const saveSelections = useCallback((cells: Set<string>) => {
    if (currentId && loadedRef.current) {
      setSelections(currentId, Array.from(cells));
    }
  }, [currentId, setSelections]);

  const saveEditableValues = useCallback((values: Record<number, string>) => {
    if (currentId && loadedRef.current && Object.keys(values).length > 0) {
      setEditableValues(currentId, values);
    }
  }, [currentId, setEditableValues]);

  const saveWalmartValidation = useCallback((validation: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setValidations(currentId, 'walmart', validation);
    }
  }, [currentId, setValidations]);

  const saveLlmValidation = useCallback((validation: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setValidations(currentId, 'llm', validation);
    }
  }, [currentId, setValidations]);

  const saveWalmartComments = useCallback((comments: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setComments(currentId, 'walmart', comments);
    }
  }, [currentId, setComments]);

  const saveLlmComments = useCallback((comments: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setComments(currentId, 'llm', comments);
    }
  }, [currentId, setComments]);

  // ✅ NEW: Save final verdicts
  const saveFinalVerdicts = useCallback((verdicts: Record<number, string>) => {
    if (currentId && loadedRef.current) {
      setFinalVerdicts(currentId, verdicts);
    }
  }, [currentId, setFinalVerdicts]);

  // ✅ Auto-save effects
  useEffect(() => {
    saveSelections(selectedCells);
  }, [selectedCells, saveSelections]);

  useEffect(() => {
    saveEditableValues(editableWalmartValues);
  }, [editableWalmartValues, saveEditableValues]);

  useEffect(() => {
    saveWalmartValidation(walmartValidation);
  }, [walmartValidation, saveWalmartValidation]);

  useEffect(() => {
    saveLlmValidation(llmValidation);
  }, [llmValidation, saveLlmValidation]);

  useEffect(() => {
    saveWalmartComments(walmartComments);
  }, [walmartComments, saveWalmartComments]);

  useEffect(() => {
    saveLlmComments(llmComments);
  }, [llmComments, saveLlmComments]);

  // ✅ NEW: Auto-save final verdicts
  useEffect(() => {
    saveFinalVerdicts(finalVerdictsState);
  }, [finalVerdictsState, saveFinalVerdicts]);

  const cleanNaNValue = (v: any): string => {
    if (v === null || v === undefined) return "-";
    if (typeof v === "string") {
      const s = v.trim();
      if (s === "" || s.toLowerCase() === "nan") return "-";
      return s;
    }
    return String(v);
  };

  // ✅ Generate auto final verdict function (excludes Brand/Competitors)
  const generateAutoFinalVerdict = useCallback((rowIdx: number): string => {
    const row = tableRows[rowIdx];
    if (!row) return "";

    const sections: string[] = [];

    // ✅ UPDATED: Only check for Walmart, Walmart_Latest, and LLM selections
    const hasWalmartSelection = selectedCells.has(`${rowIdx}-Walmart`);
    const hasWalmartLatestSelection = selectedCells.has(`${rowIdx}-Walmart_Latest`);
    const hasLlmSelection = selectedCells.has(`${rowIdx}-LLM`);

    // If none of the core sources are selected, return empty
    if (!hasWalmartSelection && !hasWalmartLatestSelection && !hasLlmSelection) {
      return "No Walmart or LLM data selected for this attribute";
    }

    // ✅ WALMART SECTION
    if (hasWalmartSelection) {
      const walmartValue = cleanNaNValue(editableWalmartValues[rowIdx] || row["Walmart"]);
      const walmartValidationValue = walmartValidation[rowIdx] || "";
      const walmartCommentValue = walmartComments[rowIdx] || "";
      const walmartUrl = competitorUrls["Walmart"] || "";

      const walmartParts = [];
      walmartParts.push(`Value: ${walmartValue}`);
      
      if (walmartUrl && walmartUrl !== "-") {
        walmartParts.push(`Source: ${walmartUrl}`);
      }
      
      if (walmartValidationValue) {
        walmartParts.push(`Validation: ${walmartValidationValue}`);
      }
      
      if (walmartCommentValue) {
        const formattedComment = walmartCommentValue.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        walmartParts.push(`Comment: ${formattedComment}`);
      }

      sections.push(`🛒 WALMART:\n${walmartParts.join("\n")}`);
    }

    // ✅ WALMART LATEST SECTION  
    if (hasWalmartLatestSelection) {
      const walmartLatestValue = cleanNaNValue(row["Walmart_Latest"]);
      const walmartLatestUrl = competitorUrls["Walmart_Latest"] || "";

      const walmartLatestParts = [];
      walmartLatestParts.push(`Value: ${walmartLatestValue}`);
      
      if (walmartLatestUrl && walmartLatestUrl !== "-") {
        walmartLatestParts.push(`Source: ${walmartLatestUrl}`);
      }

      sections.push(`🆕 WALMART LATEST:\n${walmartLatestParts.join("\n")}`);
    }

    // ✅ LLM SECTION
    if (hasLlmSelection) {
      const llmValue = cleanNaNValue(llmValues[rowIdx]);
      const llmValidationValue = llmValidation[rowIdx] || "";
      const llmCommentValue = llmComments[rowIdx] || "";
      const llmUrl = llmSourceUrl || "";

      // Try to get specific LLM source URL for this attribute
      let specificLlmUrl = llmUrl;
      try {
        const parsedData = JSON.parse(rawJson || '{}');
        const currentAttribute = tableRows[rowIdx]?.Attribute;
        const llmSourceData = parsedData.llm_suggested?.[currentAttribute];
        if (llmSourceData?.source_url) {
          specificLlmUrl = llmSourceData.source_url;
        }
      } catch (e) {
        // ignore
      }

      const llmParts = [];
      llmParts.push(`Value: ${llmValue}`);
      
      if (specificLlmUrl && specificLlmUrl !== "-" && specificLlmUrl !== "null") {
        llmParts.push(`Source: ${specificLlmUrl}`);
      }
      
      if (llmValidationValue) {
        llmParts.push(`Validation: ${llmValidationValue}`);
      }
      
      if (llmCommentValue) {
        const formattedComment = llmCommentValue.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
        llmParts.push(`Comment: ${formattedComment}`);
      }

      sections.push(`🤖 LLM:\n${llmParts.join("\n")}`);
    }

    // ✅ UPDATED: Summary section only for Walmart and LLM data
    if (sections.length > 1) {
      const selectedValues = [];
      const selectedSources = [];
      
      if (hasWalmartSelection) {
        const walmartVal = cleanNaNValue(editableWalmartValues[rowIdx] || row["Walmart"]);
        if (walmartVal !== "-") {
          selectedValues.push(walmartVal);
          selectedSources.push("Walmart");
        }
      }
      
      if (hasWalmartLatestSelection) {
        const walmartLatestVal = cleanNaNValue(row["Walmart_Latest"]);
        if (walmartLatestVal !== "-") {
          selectedValues.push(walmartLatestVal);
          selectedSources.push("Walmart_Latest");
        }
      }
      
      if (hasLlmSelection) {
        const llmVal = cleanNaNValue(llmValues[rowIdx]);
        if (llmVal !== "-") {
          selectedValues.push(llmVal);
          selectedSources.push("LLM");
        }
      }

      const allValidations = [walmartValidation[rowIdx] || "", llmValidation[rowIdx] || ""].filter(Boolean);
      const allComments = [walmartComments[rowIdx] || "", llmComments[rowIdx] || ""].filter(Boolean);

      const summaryParts = [];
      if (selectedValues.length > 0) {
        summaryParts.push(`Combined Values: ${selectedValues.join(", ")}`);
      }
      if (selectedSources.length > 0) {
        summaryParts.push(`Data Sources: ${selectedSources.join(", ")}`);
      }
      if (allValidations.length > 0) {
        summaryParts.push(`Overall Validation: ${allValidations.join(", ")}`);
      }
      if (allComments.length > 0) {
        const formattedComments = allComments.map((c: string) => c.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()));
        summaryParts.push(`Combined Comments: ${formattedComments.join(" | ")}`);
      }

      if (summaryParts.length > 0) {
        sections.push(`📊 SUMMARY:\n${summaryParts.join("\n")}`);
      }
    }

    return sections.join("\n\n");
  }, [
    tableRows, 
    selectedCells, 
    editableWalmartValues, 
    llmValues, 
    walmartValidation,
    llmValidation, 
    walmartComments, 
    llmComments, 
    competitorUrls, 
    llmSourceUrl, 
    rawJson
  ]);

  // ✅ Auto-update final verdicts
  useEffect(() => {
    if (currentId && loadedRef.current && tableRows.length > 0) {
      const updatedVerdicts: Record<number, string> = {};
      let hasChanges = false;

      tableRows.forEach((_, rowIdx) => {
        const newVerdict = generateAutoFinalVerdict(rowIdx);
        const currentVerdict = finalVerdictsState[rowIdx] || "";
        
        // Only update if there's a meaningful change and user hasn't manually edited
        if (newVerdict && newVerdict !== currentVerdict && !currentVerdict.includes("MANUAL:")) {
          updatedVerdicts[rowIdx] = newVerdict;
          hasChanges = true;
        } else if (currentVerdict) {
          updatedVerdicts[rowIdx] = currentVerdict;
        }
      });

      if (hasChanges) {
        setFinalVerdictsState(updatedVerdicts);
      }
    }
  }, [
    selectedCells, 
    editableWalmartValues, 
    walmartValidation, 
    llmValidation, 
    walmartComments, 
    llmComments, 
    llmValues,
    tableRows,
    generateAutoFinalVerdict,
    currentId,
    finalVerdictsState
  ]);

  const computeFinalValues = (): EnhancedFinalValue[] => {
    return tableRows.map((row, rowIdx) => {
      const selectedCols = Object.keys(row).filter(
        (col) =>
          !["Attribute", "Validate LLM", "Validate Walmart"].includes(col) &&
          selectedCells.has(`${rowIdx}-${col}`)
      );
      if (selectedCells.has(`${rowIdx}-LLM`)) selectedCols.push("LLM");

      if (selectedCols.length === 0) {
        return {
          Attribute: row.Attribute,
          finalValue: "-",
          source: "-",
          walmartValidation: walmartValidation[rowIdx] || "",
          llmValidation: llmValidation[rowIdx] || "",
          walmartComment: walmartComments[rowIdx] || "",
          llmComment: llmComments[rowIdx] || "",
          finalVerdict: finalVerdictsState[rowIdx] || "", // ✅ NEW
          rawData: {},
        };
      }

      const values = selectedCols
        .map((col) => {
          if (col === "LLM") return cleanNaNValue(llmValues[rowIdx]);
          if (col === "Walmart")
            return cleanNaNValue(editableWalmartValues[rowIdx] || row[col]);
          if (col === "Walmart_Latest")
            return cleanNaNValue(row[col]);
          return cleanNaNValue(row[col]);
        })
        .filter((v) => v !== "-");

      const sources = selectedCols.map((col) => {
        if (col === "LLM") {
          return llmSourceUrl || "-";
        } else if (col === "Walmart") {
          return competitorUrls["Walmart"] || "-";
        } else if (col === "Walmart_Latest") {
          return competitorUrls["Walmart_Latest"] || "-";
        } else if (col === "Brand") {
          return brandUrl || competitorUrls["Brand"] || "-";
        } else {
          return competitorUrls[col] || "-";
        }
      }).filter((url) => url !== "-");

      return {
        Attribute: row.Attribute,
        finalValue: values.length > 0 ? values.join(", ") : "-",
        source: sources.length > 0 ? sources.join(" | ") : "-",
        walmartValidation: walmartValidation[rowIdx] || "",
        llmValidation: llmValidation[rowIdx] || "",
        walmartComment: walmartComments[rowIdx] || "",
        llmComment: llmComments[rowIdx] || "",
        finalValidation:
          [walmartValidation[rowIdx] || "", llmValidation[rowIdx] || ""]
            .filter(Boolean)
            .join(", ") || "-",
        finalComment:
          [walmartComments[rowIdx] || "", llmComments[rowIdx] || ""]
            .filter(Boolean)
            .map((c) =>
              c.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
            )
            .join(" | ") || "-",
        finalVerdict: finalVerdictsState[rowIdx] || "", // ✅ NEW
        rawData: {},
      };
    });
  };

  const finalValues = computeFinalValues();

  // ✅ ENHANCED CSV Export with ALL competitor values and Final Verdict
  const exportToWideFormatCSV = () => {
    try {
      const parsedResponse = JSON.parse(rawJson || "{}");
      const wideData: Record<string, string> = {};
      
      // User info
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const u = JSON.parse(storedUser);
          const submittedBy = u?.username || u?.name || u?.email || '';
          if (submittedBy) wideData['user'] = String(submittedBy);
        }
      } catch (e) {
        // ignore
      }

      // ✅ Basic info
      wideData['Timestamp'] = new Date().toISOString();
      wideData['Submission ID'] = preloadedRow?.id_str || '';
      wideData["Walmart URL"] = cleanNaNValue(parsedResponse.walmart?.url);
      wideData["Walmart_Latest URL"] = cleanNaNValue(parsedResponse.walmart_llm?.url);
      wideData["Product Type"] = cleanNaNValue(parsedResponse.product_type);
      wideData["Category"] = cleanNaNValue(parsedResponse.category);
      wideData["GTIN"] = cleanNaNValue(preloadedRow?.product_id);
      wideData["Product Name"] = cleanNaNValue(preloadedRow?.product_name);
      wideData["Item ID"] = cleanNaNValue(preloadedRow?.item_id);
      wideData["Final LLM Link"] = cleanNaNValue(llmSourceUrl);

      // ✅ Get all competitor columns dynamically
      const allCompetitors = new Set<string>();
      if (tableRows.length > 0) {
        Object.keys(tableRows[0]).forEach(col => {
          if (!["Attribute", "Walmart", "Walmart_Latest", "LLM", "Brand"].includes(col)) {
            allCompetitors.add(col);
          }
        });
      }

      // ✅ Add competitor source URLs to basic info
      allCompetitors.forEach(competitor => {
        wideData[`${competitor} Source URL`] = cleanNaNValue(competitorUrls[competitor] || "");
      });

      // Process all attributes
      const allAttributes = new Set<string>();
      tableRows.forEach(r => { if (r.Attribute) allAttributes.add(r.Attribute); });
      
      Array.from(allAttributes).forEach(attribute => {
        const rowIdx = tableRows.findIndex(r => r.Attribute === attribute);
        
        // ✅ Original data columns
        wideData[`${attribute} Initial prefilled values`] = cleanNaNValue(parsedResponse.walmart?.attributes?.[attribute]);
        wideData[`${attribute} Walmart_Latest values`] = cleanNaNValue(parsedResponse.walmart_llm?.attributes?.[attribute]);
        
        // ✅ LLM data
        const llmData = parsedResponse.llm_suggested?.[attribute];
        wideData[`${attribute} LLM Suggested value`] = cleanNaNValue(llmData?.value || llmData);
        wideData[`${attribute} LLM_suggested_url`] = cleanNaNValue(llmData?.source_url || "-");
        
        // ✅ Current values (edited or original)
        wideData[`${attribute}_Walmart_value`] = rowIdx !== -1 ? cleanNaNValue(editableWalmartValues[rowIdx] || tableRows[rowIdx]["Walmart"]) : "-";
        wideData[`${attribute}_Brand_value`] = cleanNaNValue(parsedResponse.brand?.attributes?.[attribute]);
        wideData[`${attribute}_WalmartLatest_value`] = rowIdx !== -1 ? cleanNaNValue(tableRows[rowIdx]["Walmart_Latest"]) : "-";
        wideData[`${attribute}_LLM_value`] = rowIdx !== -1 ? cleanNaNValue(llmValues[rowIdx]) : "-";

        // ✅ Add ALL competitor values
        allCompetitors.forEach(competitor => {
          wideData[`${attribute}_${competitor}_value`] = rowIdx !== -1 ? cleanNaNValue(tableRows[rowIdx][competitor] || "") : "-";
        });
        
        // ✅ Validation data
        wideData[`${attribute}_Walmart_Validation`] = rowIdx !== -1 ? cleanNaNValue(walmartValidation[rowIdx]) : "-";
        wideData[`${attribute}_LLM_Validation`] = rowIdx !== -1 ? cleanNaNValue(llmValidation[rowIdx]) : "-";
        wideData[`${attribute}_Final_Validation`] = rowIdx !== -1 ? ([walmartValidation[rowIdx] || "", llmValidation[rowIdx] || ""].filter(Boolean).join(", ") || "-") : "-";
        
        // ✅ Comments data
        wideData[`${attribute}_Walmart_Comment`] = rowIdx !== -1 ? cleanNaNValue(walmartComments[rowIdx]) : "-";
        wideData[`${attribute}_LLM_Comment`] = rowIdx !== -1 ? cleanNaNValue(llmComments[rowIdx]) : "-";
        wideData[`${attribute}_Final_Comment`] = rowIdx !== -1 ? ([walmartComments[rowIdx] || "", llmComments[rowIdx] || ""].filter(Boolean).map(c=>c.replace(/-/g,' ').replace(/\b\w/g,l=>l.toUpperCase())).join(' | ') || "-") : "-";
        
        // ✅ Final Verdict (the new column)
        wideData[`${attribute}_Final_Verdict`] = rowIdx !== -1 ? cleanNaNValue(finalVerdictsState[rowIdx] || "") : "-";
      });

      // ✅ Final combined values
      finalValues.forEach(fv => {
        if (fv.Attribute) {
          wideData[`${fv.Attribute}_Final_Choice`] = cleanNaNValue(fv.finalValue);
          wideData[`${fv.Attribute}_Final_Source`] = cleanNaNValue(fv.source);
          wideData[`${fv.Attribute}_Final_Verdict_Combined`] = cleanNaNValue(fv.finalVerdict || "");
          
          // ✅ Add selection status (including all competitors)
          const rowIdx = tableRows.findIndex(r => r.Attribute === fv.Attribute);
          if (rowIdx !== -1) {
            const selectedSources = [];
            if (selectedCells.has(`${rowIdx}-Walmart`)) selectedSources.push("Walmart");
            if (selectedCells.has(`${rowIdx}-Walmart_Latest`)) selectedSources.push("Walmart_Latest");  
            if (selectedCells.has(`${rowIdx}-LLM`)) selectedSources.push("LLM");
            if (selectedCells.has(`${rowIdx}-Brand`)) selectedSources.push("Brand");
            
            // ✅ Add ALL competitor selections
            allCompetitors.forEach(competitor => {
              if (selectedCells.has(`${rowIdx}-${competitor}`)) {
                selectedSources.push(competitor);
              }
            });
            
            wideData[`${fv.Attribute}_Selected_Sources`] = selectedSources.join(", ") || "-";
          }
        }
      });

      // ✅ Summary statistics
      wideData['Total_Attributes'] = String(tableRows.length);
      wideData['Total_Selections'] = String(Array.from(selectedCells).length);
      wideData['Total_Competitors'] = String(allCompetitors.size);
      wideData['Competitor_Names'] = Array.from(allCompetitors).join(", ") || "-";
      wideData['Walmart_Yes_Count'] = String(Object.values(walmartValidation).filter(v => v === 'Yes').length);
      wideData['Walmart_No_Count'] = String(Object.values(walmartValidation).filter(v => v === 'No').length);
      wideData['LLM_Yes_Count'] = String(Object.values(llmValidation).filter(v => v === 'Yes').length);
      wideData['LLM_No_Count'] = String(Object.values(llmValidation).filter(v => v === 'No').length);
      wideData['Final_Verdicts_Count'] = String(Object.values(finalVerdictsState).filter(v => v && v.trim()).length);

      const headers = Object.keys(wideData);
      const values = Object.values(wideData);
      const csvContent = [
        headers.join(","), 
        values.map(v => `"${String(v).replace(/"/g,'""').replace(/\n/g, ' ').replace(/\r/g, ' ')}"`).join(",")
      ].join("\n");
      
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `complete-walmart-analysis-${preloadedRow?.id_str || 'unknown'}-${Date.now()}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      console.log(`✅ CSV exported with ${headers.length} columns including Final Verdict and ${allCompetitors.size} competitors`);
      console.log(`✅ Competitors included: ${Array.from(allCompetitors).join(', ')}`);
    } catch (e) {
      console.error("export failed", e);
    }
  };

  // ✅ FIXED fetchResponse with proper initial loading
  const fetchResponse = async (): Promise<void> => {
    console.log('📡 fetchResponse called for:', preloadedRow?.id_str);
    setLoading(true);
    try {
      setTableRows([]);
      setEditableWalmartValues({});
      setLlmValues({});
      setCompetitorUrls({});
      setLlmSourceUrl("");
      setAttrMap({});
      setBrandUrl("");
      setRawJson("");

      if (!preloadedRow?.id_str) { 
        console.warn('❌ fetchResponse called without preloadedRow.id_str', preloadedRow); 
        return; 
      }

      const form = new FormData();
      const prompt = `Please return cached product data for ${preloadedRow?.website_link || preloadedRow?.product_name || ''}`;
      const model = 'gemini-1.5-flash';
      form.append('prompt', prompt);
      form.append('model', model);
      form.append('website_link', String(preloadedRow.website_link || ''));
      form.append('id_str', String(preloadedRow.id_str));

      console.debug('🚀 POST /query/ form fields:', { prompt, model, website_link: preloadedRow.website_link, id_str: preloadedRow.id_str });
      const r = await fetch(`${API_BASE_URL}/query/`, { method: 'POST', body: form });
      const text = await r.text();
      let data: any = {};
      try { data = JSON.parse(text); } catch (err) { console.warn('⚠️ Non-JSON response from /query/:', text); }
      if (!r.ok) {
        const errMsg = (data && data.error) ? data.error : `HTTP ${r.status}`;
        throw new Error(`fetch failed: ${errMsg}`);
      }

      let rows: any[] = data.table || data.attributes || [];

      console.log("Bhati",data)
      if ((!rows || rows.length === 0) && data.walmart) {
        console.log('📊 Building rows from walmart data...');
        const walmartAttrs = (data.walmart && data.walmart.attributes) || {};
        const brandAttrs = (data.brand && data.brand.attributes) || {};
        const competitors = data.competitors || [];
        const walmartLlmAttrs = (data.walmart_llm && data.walmart_llm.attributes) || {};

        const makeSafeName = (u: string) => {
          try {
            const hostname = new URL(u).hostname || "";
            return hostname.replace(/^www\./, "").split(".")[0] || u;
          } catch (e) {
            return u || "competitor";
          }
        };

        const extractedUrls: Record<string, string> = {};
        extractedUrls['Walmart'] = data.walmart.url || '';
        extractedUrls['Walmart_Latest'] = data.walmart_llm?.url || '';
        if (data.brand?.url) extractedUrls['Brand'] = data.brand.url;
        competitors.forEach((c: any) => {
          extractedUrls[makeSafeName(c.url)] = c.url || '';
        });

        const allAttrs = new Set<string>();
        Object.keys(walmartAttrs || {}).forEach(k => allAttrs.add(k));
        Object.keys(brandAttrs || {}).forEach(k => allAttrs.add(k));
        Object.keys(walmartLlmAttrs || {}).forEach(k => allAttrs.add(k));
        competitors.forEach((c: any) => { 
          Object.keys((c && c.attributes) || {}).forEach(k => allAttrs.add(k)); 
        });

        const extras = ["brand", "mpn", "product_type", "vehicle_make", "vehicle_model", "vehicle_year", "query", "Product Long Description", "Product Short Description", "Product Name", "Product Type"];
        extras.forEach(e => allAttrs.add(e));

        const builtRows: any[] = [];
        Array.from(allAttrs).forEach(attribute => {
          const row: Record<string, any> = { Attribute: attribute };
          row["Walmart"] = (walmartAttrs && (walmartAttrs[attribute] ?? (data.walmart.extra && data.walmart.extra[attribute]))) || "";
          row["Walmart_Latest"] = (walmartLlmAttrs && walmartLlmAttrs[attribute]) || "";
          row["Brand"] = (brandAttrs && (brandAttrs[attribute] ?? (data.brand && data.brand.extra && data.brand.extra[attribute]))) || "";
          competitors.forEach((c: any) => {
            const name = makeSafeName(c.url);
            row[name] = ((c && c.attributes && (c.attributes[attribute])) || (c && c.extra && c.extra[attribute])) || "";
          });
          builtRows.push(row);
        });

        rows = builtRows;
        setCompetitorUrls(extractedUrls);
      } else if (rows && rows.length > 0) {
        const sourceUrlRow = rows.find(r => r.Attribute === 'Source URL');
        const extractedUrls: Record<string, string> = {};

        if (sourceUrlRow) {
          Object.keys(sourceUrlRow).forEach(col => {
            if (col !== 'Attribute' && sourceUrlRow[col]) {
              extractedUrls[col] = sourceUrlRow[col];
            }
          });
          
          rows = rows.filter(r => r.Attribute !== 'Source URL');
        }

        setCompetitorUrls({ ...competitorUrls, ...extractedUrls });
      }

      setTableRows(rows || []);
      setRawJson(JSON.stringify(data || {}));

      const initEditable: Record<number, string> = {};
      const initOriginal: Record<number, string> = {};
      const initLlm: Record<number, string> = {};
      
      (rows || []).forEach((row: any, idx: number) => {
        initOriginal[idx] = cleanNaNValue(row.Walmart || '-');
        initEditable[idx] = cleanNaNValue(row.Walmart || '-');
        initLlm[idx] = cleanNaNValue((data.llm_suggested && data.llm_suggested[row.Attribute]?.value) || row.LLM || '-');
      });
      
      setOriginalWalmartValues(initOriginal);
      if (Object.keys(editableWalmartValues).length === 0) {
        setEditableWalmartValues(initEditable);
      }
      setLlmValues(initLlm);
      setBrandUrl(data.brand?.url || '');
      setLlmSourceUrl(data.llm_source_url || '');
      setAttrMap(data.attr_map || {});
      
    } catch (e) {
      console.error('❌ fetchResponse failed', e);
    } finally {
      setLoading(false);
    }
  };

  const generateInsights = async (): Promise<void> => {
    setInsightsLoading(true);
    try {
      const selectedData: any[] = [];
      tableRows.forEach((row, rowIdx) => {
        const selectedCols = Object.keys(row).filter((col) => !["Attribute","Validate LLM","Validate Walmart","Prefilled Value Comments","LLM Comment","Final Validation","Final Comment","Final Value","Source","Final Verdict"].includes(col) && selectedCells.has(`${rowIdx}-${col}`));
        if (selectedCells.has(`${rowIdx}-LLM`)) selectedCols.push("LLM");
        if (selectedCols.length > 0) {
          const selectedValues = selectedCols.map(col => {
            if (col === 'LLM') return cleanNaNValue(llmValues[rowIdx]);
            if (col === 'Walmart') return cleanNaNValue(editableWalmartValues[rowIdx] || row[col]);
            if (col === 'Walmart_Latest') return cleanNaNValue(row[col]);
            return cleanNaNValue(row[col]);
          }).filter(v => v !== '-');
          selectedData.push({ 
            attribute: row.Attribute || `Unknown${rowIdx}`, 
            selectedSources: selectedCols, 
            selectedValues, 
            walmartValidation: walmartValidation[rowIdx] || '', 
            llmValidation: llmValidation[rowIdx] || '', 
            walmartComment: walmartComments[rowIdx] || '', 
            llmComment: llmComments[rowIdx] || '',
            finalVerdict: finalVerdictsState[rowIdx] || '' // ✅ NEW
          });
        }
      });

      const validationSummary = { 
        walmart_yes: Object.values(walmartValidation).filter(v=>v==='yes').length, 
        walmart_no: Object.values(walmartValidation).filter(v=>v==='no').length, 
        llm_yes: Object.values(llmValidation).filter(v=>v==='yes').length, 
        llm_no: Object.values(llmValidation).filter(v=>v==='no').length, 
        total_selected: selectedData.length 
      };

      const parsedResponse = JSON.parse(rawJson || '{}');
      const productInfo = { 
        product_name: preloadedRow?.product_name || 'Unknown Product', 
        category: parsedResponse.category || preloadedRow?.category || 'Unknown Category', 
        walmart_url: parsedResponse.walmart?.url || preloadedRow?.website_link || '', 
        product_type: parsedResponse.product_type || preloadedRow?.product_type || '' 
      };

      const requestPayload = { 
        product_name: productInfo.product_name, 
        category: productInfo.category, 
        product_attributes: {}, 
        site_summaries: selectedData, 
        finalValues, 
        attributeCount: tableRows.length, 
        validationSummary, 
        shortDescription: finalValues.find(i=>i.Attribute==='Short Description')?.finalValue||'', 
        longDescription: finalValues.find(i=>i.Attribute==='Product Long Description')?.finalValue||'' 
      };
      const resp = await fetch(`${API_BASE_URL}/claim/process_product`, { 
        method: 'POST', 
        headers: { 'Content-Type': 'application/json' }, 
        body: JSON.stringify(requestPayload) 
      });
      
      if (!resp.ok) { 
        const txt = await resp.text(); 
        throw new Error(`Insights API failed: ${txt}`); 
      }
      
      const apiResult = await resp.json();
      setShortSummary(apiResult.short_summary || 'Analysis completed successfully.');
      setDetailedSummary(apiResult.long_summary || 'Detailed insights have been generated based on your selected data.');
      setInsightsGenerated(true);
    } catch (e) {
      console.warn('insights failed, using fallback', e);
      setShortSummary(`Analysis of ${tableRows.length} attributes completed. ${Array.from(selectedCells).length} selections made.`);
      setDetailedSummary(`Competitive analysis has been performed on the selected product attributes.`);
      setInsightsGenerated(true);
    } finally {
      setInsightsLoading(false);
    }
  };

  const handleCellToggle = (rowIdx: number, col: string) => {
    const key = `${rowIdx}-${col}`;
    const updated = new Set(selectedCells);
    if (updated.has(key)) updated.delete(key); else updated.add(key);
    setSelectedCells(updated);
  };

  const handleRowToggle = (rowIdx: number, row: Record<string,string>) => {
    const updated = new Set(selectedCells);
    const rowKeys = Object.keys(row).filter(col => !['Attribute','Validate LLM','Validate Walmart','Prefilled Value Comments','LLM Comment','Final Verdict'].includes(col)).map(col => `${rowIdx}-${col}`).concat([`${rowIdx}-LLM`]);
    const allSelected = rowKeys.every(k => updated.has(k));
    if (allSelected) rowKeys.forEach(k=>updated.delete(k)); else rowKeys.forEach(k=>updated.add(k));
    setSelectedCells(updated);
  };

  const clearSelection = () => { 
    setSelectedCells(new Set()); 
    setEditableWalmartValues({});
    setWalmartValidation({});
    setLlmValidation({});
    setWalmartComments({});
    setLlmComments({});
    setFinalVerdictsState({}); 
    
    if (currentId) {
      clearSelections(currentId);
    }
  };


  return (
    <div className="h-screen flex flex-col">
      <div className="rounded-2xl bg-white dark:bg-gray-900 shadow-lg border border-gray-200 dark:border-gray-700 transition-colors overflow-hidden flex-1 flex flex-col">
        <ResponseControls 
          preloadedRow={preloadedRow} 
          loading={loading} 
          fetchResponse={fetchResponse} 
          exportToWideFormatCSV={exportToWideFormatCSV} 
          generateInsights={generateInsights} 
          clearSelection={clearSelection} 
        />
        
        {loading && (
          <div className="flex items-center gap-4 text-gray-600 dark:text-gray-400 p-8 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
            <Loader2 className="animate-spin w-6 h-6 text-blue-500" />
            <span className="italic font-medium">Fetching and analyzing product data...</span>
          </div>
        )}
        
        <div className="p-6 flex-1 overflow-auto">
          <ResponseTable
            tableRows={tableRows}
            selectedCells={selectedCells}
            handleCellToggle={handleCellToggle}
            handleRowToggle={handleRowToggle}
            editableWalmartValues={editableWalmartValues}
            originalWalmartValues={originalWalmartValues}
            editingCell={editingCell}
            setEditingCell={setEditingCell}
            saveEdit={(rowIdx: number) => {
              setEditingCell(null); 
              setTableRows(prev => prev.map((r, i)=> i===rowIdx ? { ...r, Walmart: cleanNaNValue(editableWalmartValues[rowIdx]) } : r )); 
            }}
            cancelEdit={()=> setEditingCell(null)}
            setEditableWalmartValues={setEditableWalmartValues}
            llmValues={llmValues}
            rawJson={rawJson}
            competitorUrls={competitorUrls}
            brandUrl={brandUrl}
            llmSourceUrl={llmSourceUrl}
            walmartValidation={walmartValidation}
            llmValidation={llmValidation}
            setWalmartValidation={setWalmartValidation}
            setLlmValidation={setLlmValidation}
            walmartComments={walmartComments}
            llmComments={llmComments}
            setWalmartComments={setWalmartComments}
            setLlmComments={setLlmComments}
            finalValues={finalValues}
            loading={loading}
            attrMap={attrMap}
            exportToWideFormatCSV={exportToWideFormatCSV}
            generateInsights={generateInsights}
            insightsLoading={insightsLoading}
            finalVerdictsState={finalVerdictsState}
            setFinalVerdictsState={setFinalVerdictsState}
            editingFinalVerdict={editingFinalVerdict}
            setEditingFinalVerdict={setEditingFinalVerdict}
            generateAutoFinalVerdict={generateAutoFinalVerdict}
          />

          {!insightsLoading && insightsGenerated && (shortSummary || detailedSummary) && (
            <div className="space-y-8 transition-all duration-500 mb-10 mt-6">
              {shortSummary && (
                <div className="p-8 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-2xl border border-blue-200 dark:border-blue-700 shadow-2xl">
                  <h3 className="font-extrabold text-2xl md:text-3xl text-blue-800 dark:text-blue-300 mb-3">Short Summary</h3>
                  <p className="text-gray-800 dark:text-gray-200 leading-relaxed text-base md:text-lg">
                    {formatSummary(shortSummary, { mode: 'short' }) as string}
                  </p>
                </div>
              )}

              {detailedSummary && (
                <div className="p-8 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/50 dark:to-emerald-900/50 rounded-2xl border border-green-200 dark:border-green-700 shadow-2xl">
                  <h3 className="font-extrabold text-2xl md:text-3xl text-green-800 dark:text-green-300 mb-3">Detailed Summary</h3>
                  <ul className="list-disc list-inside text-gray-800 dark:text-gray-200 leading-relaxed space-y-2 text-sm md:text-base">
                    {(formatSummary(detailedSummary, { mode: 'list' }) as string[]).map((point, idx) => (
                      <li key={idx} className="pl-1">{point}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {tableRows.length > 0 && !loading && (
            <FeedbackForm 
              responseText={rawJson} 
              id_str={preloadedRow?.id_str || ''} 
              finalValues={finalValues} 
              shortSummary={shortSummary} 
              detailedSummary={detailedSummary} 
            />
          )}
        </div>
      </div>
    </div>
  );
}
